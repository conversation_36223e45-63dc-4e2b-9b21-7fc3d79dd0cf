from django.db import models
from django.conf import settings

class EventTemplate(models.Model):
    """
    Represents a user-created template for a sequence of events.
    e.g., "公司年会流程", "线上破冰环节"
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='event_templates')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"'{self.name}' by {self.creator.username}"


class EventStep(models.Model):
    """
    Represents a single step within an EventTemplate.
    """
    # --- Step Type Choices ---
    # Basic step types (available to all users)
    STEP_GAME_PICTIONARY = 'GAME_PICTIONARY'
    STEP_FREE_CHAT = 'FREE_CHAT'

    # Premium step types (Pro/Max only - "选择性环节")
    STEP_PAUSE = 'PAUSE'  # 暂停环节
    STEP_SPEECH = 'SPEECH'  # 发言环节
    STEP_CUSTOM = 'CUSTOM'  # 自定义环节
    STEP_POLL = 'POLL'  # 投票环节
    STEP_QNA = 'QNA'  # 问答环节

    STEP_TYPE_CHOICES = [
        (STEP_GAME_PICTIONARY, '游戏：你画我猜'),
        (STEP_FREE_CHAT, '自由讨论'),
        # Premium features
        (STEP_PAUSE, '暂停环节'),
        (STEP_SPEECH, '发言环节'),
        (STEP_CUSTOM, '自定义环节'),
        (STEP_POLL, '投票环节'),
        (STEP_QNA, '问答环节'),
    ]

    # Define which step types require premium subscription
    PREMIUM_STEP_TYPES = {
        STEP_PAUSE, STEP_SPEECH, STEP_CUSTOM, STEP_POLL, STEP_QNA
    }

    template = models.ForeignKey(EventTemplate, on_delete=models.CASCADE, related_name='steps')
    name = models.CharField(max_length=100, blank=True, help_text="Custom name for this step")
    order = models.PositiveIntegerField() # The sequence of this step in the template
    step_type = models.CharField(max_length=50, choices=STEP_TYPE_CHOICES)
    
    # A flexible field to store settings for each step type.
    # e.g., for a game, it might store the number of rounds.
    # For a poll, it might store the question and options.
    configuration = models.JSONField(default=dict, blank=True)
    
    duration = models.PositiveIntegerField(default=300, help_text="Duration of this step in seconds")

    class Meta:
        # Steps should be ordered within a template
        ordering = ['template', 'order']

    def __str__(self):
        display_name = self.name if self.name else self.get_step_type_display()
        return f"Step {self.order}: {display_name} for '{self.template.name}'"
