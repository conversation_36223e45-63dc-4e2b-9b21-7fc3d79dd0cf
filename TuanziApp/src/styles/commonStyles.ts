/**
 * 通用样式系统
 *
 * 基于主题系统的通用样式，提供一致的设计语言
 */

import { StyleSheet } from 'react-native';
import { theme } from './theme';

// 保持向后兼容的颜色导出
export const colors = {
    primary: theme.colors.primary,
    background: theme.colors.surface,
    text: theme.colors.textPrimary,
    lightGray: theme.colors.gray100,
    mediumGray: theme.colors.gray300,
    darkGray: theme.colors.gray600,
    white: theme.colors.white,
    black: theme.colors.black,
    myMessageBubble: '#dcf8c6',
    error: theme.colors.error,
    success: theme.colors.success,
};

export const commonStyles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: colors.background,
    },
    container: {
        flex: 1,
        backgroundColor: colors.background,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        marginBottom: 24,
        color: colors.text,
    },
    subtitle: {
        fontSize: 20,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 10,
        color: colors.darkGray,
    },
    input: {
        width: '90%',
        height: 44,
        borderColor: colors.mediumGray,
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 12,
        paddingHorizontal: 15,
        fontSize: 16,
    },
    buttonContainer: {
        marginTop: 10,
        width: '90%',
    },
    linkText: {
        color: colors.primary,
        marginTop: 20,
        fontSize: 16,
    },
});
