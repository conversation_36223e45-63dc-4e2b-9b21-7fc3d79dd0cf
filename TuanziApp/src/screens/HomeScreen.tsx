import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, TextInput, Alert } from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { API_URL } from '../api/client';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

export const HomeScreen = () => {
  const { user, logout, token } = useAuth();
  const { subscriptionInfo } = useSubscription();
  const [roomCodeInput, setRoomCodeInput] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();

  const handleJoinRoom = async () => {
    if (!roomCodeInput) return;
    try {
      const response = await fetch(`${API_URL}/api/rooms/join/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ room_code: roomCodeInput }),
      });
      const data = await response.json();
      if (response.ok) {
        navigation.navigate('Room', { room: data });
      } else {
        Alert.alert('加入失败', data.error || '无法加入房间。');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('错误', '加入房间时发生错误。');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>欢迎, {user?.username}!</Text>

      {/* 订阅状态显示 */}
      <View style={styles.subscriptionStatus}>
        <Text style={styles.subscriptionText}>
          当前订阅: {subscriptionInfo?.current_level || 'Free'}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button title="创建新房间" onPress={() => navigation.navigate('CreateRoom')} />
      </View>

      <View style={styles.buttonContainer}>
        <Button title="打开环节设计器" onPress={() => navigation.navigate('EventDesigner')} />
      </View>

      <View style={styles.buttonContainer}>
        <Button title="订阅管理" onPress={() => navigation.navigate('Subscription')} />
      </View>

      <View style={styles.joinSection}>
        <Text style={styles.subtitle}>或加入一个房间</Text>
        <TextInput style={styles.input} placeholder="输入房间代码" value={roomCodeInput} onChangeText={setRoomCodeInput} autoCapitalize="characters" maxLength={6} />
        <Button title="加入房间" onPress={handleJoinRoom} />
      </View>

      <View style={styles.logoutButton}>
        <Button title="登出" onPress={logout} color="red" />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
  subscriptionStatus: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 8,
    marginBottom: 20
  },
  subscriptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333'
  },
  subtitle: { fontSize: 18, marginTop: 15, marginBottom: 15 },
  input: { width: '80%', height: 40, borderColor: 'gray', borderWidth: 1, borderRadius: 5, marginBottom: 10, paddingHorizontal: 10, textAlign: 'center' },
  joinSection: { width: '100%', marginTop: 30, alignItems: 'center', borderTopWidth: 1, paddingTop: 20, borderColor: '#eee' },
  buttonContainer: { marginBottom: 10, width: '80%' },
  logoutButton: { position: 'absolute', bottom: 50 },
});
