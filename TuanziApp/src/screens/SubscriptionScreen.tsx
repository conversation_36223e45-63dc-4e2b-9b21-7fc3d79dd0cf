import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Alert, 
  ScrollView,
  Switch
} from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { commonStyles } from '../styles/commonStyles';

type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

interface SubscriptionPlan {
  level: SubscriptionLevel;
  name: string;
  price: number;
  priceText: string;
  features: string[];
  roomLimit: string;
  timeLimit: string;
  color: string;
}

export const SubscriptionScreen = () => {
  const { user } = useAuth();
  const { subscriptionInfo, upgradeSubscription, getPlans, isLoading } = useSubscription();
  const [isDebugMode, setIsDebugMode] = useState(__DEV__);

  const plans = getPlans();
  const currentPlan = plans.find(plan => plan.level === (subscriptionInfo?.current_level || 'Free')) || plans[0];

  const handleUpgrade = async (targetLevel: SubscriptionLevel) => {
    if (targetLevel === subscriptionInfo?.current_level) {
      Alert.alert('提示', '您已经是该订阅等级了');
      return;
    }

    const targetPlan = plans.find(plan => plan.level === targetLevel);
    if (!targetPlan) return;

    Alert.alert(
      '升级订阅',
      `确定要升级到${targetPlan.name}吗？\n价格：${targetPlan.priceText}`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确认升级',
          onPress: async () => {
            const success = await upgradeSubscription(targetLevel, false);
            if (success) {
              Alert.alert('升级成功', `已成功升级到${targetPlan.name}！`);
            }
          }
        }
      ]
    );
  };

  const handleDebugLevelChange = async (level: SubscriptionLevel) => {
    if (!isDebugMode) return;

    Alert.alert(
      '开发调试',
      `切换到${level}等级？（仅开发模式）`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确认',
          onPress: async () => {
            const success = await upgradeSubscription(level, true);
            if (success) {
              Alert.alert('调试功能', `已切换到${level}等级（调试模式）`);
            }
          }
        }
      ]
    );
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = plan.level === subscriptionInfo?.current_level;
    const isUpgrade = plans.findIndex(p => p.level === plan.level) >
                     plans.findIndex(p => p.level === (subscriptionInfo?.current_level || 'Free'));

    return (
      <View key={plan.level} style={[styles.planCard, isCurrentPlan && styles.currentPlanCard]}>
        <View style={[styles.planHeader, { backgroundColor: plan.color }]}>
          <Text style={styles.planName}>{plan.name}</Text>
          <Text style={styles.planPrice}>{plan.priceText}</Text>
          {isCurrentPlan && (
            <View style={styles.currentBadge}>
              <Text style={styles.currentBadgeText}>当前</Text>
            </View>
          )}
        </View>
        
        <View style={styles.planContent}>
          <View style={styles.limitsSection}>
            <Text style={styles.limitText}>房间限制：{plan.roomLimit}</Text>
            <Text style={styles.limitText}>时长限制：{plan.timeLimit}</Text>
          </View>
          
          <View style={styles.featuresSection}>
            <Text style={styles.featuresTitle}>功能特性：</Text>
            {plan.features.map((feature, index) => (
              <Text key={index} style={styles.featureText}>• {feature}</Text>
            ))}
          </View>
          
          <View style={styles.actionSection}>
            {!isCurrentPlan && (
              <TouchableOpacity
                style={[styles.actionButton, isUpgrade ? styles.upgradeButton : styles.downgradeButton]}
                onPress={() => handleUpgrade(plan.level)}
              >
                <Text style={styles.actionButtonText}>
                  {isUpgrade ? '升级' : '降级'}
                </Text>
              </TouchableOpacity>
            )}
            
            {isDebugMode && (
              <TouchableOpacity
                style={styles.debugButton}
                onPress={() => handleDebugLevelChange(plan.level)}
              >
                <Text style={styles.debugButtonText}>调试切换</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={commonStyles.title}>订阅管理</Text>
      
      {/* 当前订阅状态 */}
      <View style={styles.currentStatusCard}>
        <Text style={styles.statusTitle}>当前订阅</Text>
        <Text style={[styles.statusLevel, { color: currentPlan.color }]}>
          {currentPlan.name}
        </Text>
        <Text style={styles.statusPrice}>{currentPlan.priceText}</Text>
      </View>

      {/* 开发调试开关 */}
      {__DEV__ && (
        <View style={styles.debugSection}>
          <View style={styles.debugHeader}>
            <Text style={styles.debugTitle}>开发调试模式</Text>
            <Switch
              value={isDebugMode}
              onValueChange={setIsDebugMode}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={isDebugMode ? '#f5dd4b' : '#f4f3f4'}
            />
          </View>
          <Text style={styles.debugDescription}>
            开启后可以直接切换订阅等级进行功能测试
          </Text>
        </View>
      )}

      {/* 订阅计划 */}
      <Text style={styles.sectionTitle}>选择订阅计划</Text>
      {plans.map(renderPlanCard)}
      
      {/* 订阅说明 */}
      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>订阅说明</Text>
        <Text style={styles.infoText}>• 订阅立即生效，按月计费</Text>
        <Text style={styles.infoText}>• 可随时取消，到期后自动降级</Text>
        <Text style={styles.infoText}>• 升级后立即享受新等级的所有功能</Text>
        <Text style={styles.infoText}>• 如有问题请联系客服支持</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  currentStatusCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  statusLevel: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusPrice: {
    fontSize: 16,
    color: '#888',
  },
  debugSection: {
    backgroundColor: '#fff3cd',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  debugHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#856404',
  },
  debugDescription: {
    fontSize: 14,
    color: '#856404',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  planCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentPlanCard: {
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  planHeader: {
    padding: 15,
    position: 'relative',
  },
  planName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  planPrice: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  currentBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  currentBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  planContent: {
    padding: 15,
  },
  limitsSection: {
    marginBottom: 15,
  },
  limitText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  featuresSection: {
    marginBottom: 15,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  featureText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  actionSection: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  upgradeButton: {
    backgroundColor: '#007AFF',
  },
  downgradeButton: {
    backgroundColor: '#FF6B35',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  debugButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: '#FFC107',
  },
  debugButtonText: {
    color: '#000',
    fontSize: 14,
    fontWeight: '600',
  },
  infoSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginTop: 10,
    marginBottom: 30,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
});
