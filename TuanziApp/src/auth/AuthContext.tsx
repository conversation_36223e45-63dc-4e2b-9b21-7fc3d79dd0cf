import React, { createContext, useState, useContext, ReactNode } from 'react';
import { API_URL } from '../api/client';
import * as authStorage from './storage';
// No need to import jwt-decode here anymore as it's handled in storage.ts

interface User {
  username: string;
  subscription_level: 'Free' | 'Pro' | 'Max';
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  restoreUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const response = await fetch(`${API_URL}/api/token/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });
      const data = await response.json();
      if (response.ok && data.access) {
        // storage.ts now handles decoding
        await authStorage.storeToken(data.access);
        setToken(data.access);

        // Decode token to get subscription level
        const userData = await authStorage.getUser();
        if (userData) {
          setUser({
            username: userData.username,
            subscription_level: userData.subscription_level
          });
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error(error);
      return false;
    }
  };

  const logout = () => {
    authStorage.removeToken();
    setUser(null);
    setToken(null);
  };

  const restoreUser = async () => {
    const userData = await authStorage.getUser();
    if (userData) {
      setUser({
        username: userData.username,
        subscription_level: userData.subscription_level
      });
      setToken(userData.token);
    }
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout, restoreUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};