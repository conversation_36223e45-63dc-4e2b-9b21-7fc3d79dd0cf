import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '../auth/AuthContext';
import { 
  getSubscriptionInfo, 
  updateSubscriptionLevel, 
  SubscriptionInfo,
  getSubscriptionPlans,
  checkSubscriptionPermission
} from '../api/subscriptionApi';

type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

interface SubscriptionContextType {
  subscriptionInfo: SubscriptionInfo | null;
  isLoading: boolean;
  error: string | null;
  refreshSubscriptionInfo: () => Promise<void>;
  upgradeSubscription: (targetLevel: SubscriptionLevel, isDebug?: boolean) => Promise<boolean>;
  checkPermission: (requiredLevel: SubscriptionLevel) => boolean;
  getPlans: () => ReturnType<typeof getSubscriptionPlans>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, token, login } = useAuth();
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshSubscriptionInfo = async () => {
    if (!token) return;

    setIsLoading(true);
    setError(null);

    try {
      const info = await getSubscriptionInfo(token);
      setSubscriptionInfo(info);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load subscription info');
      console.error('Failed to refresh subscription info:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const upgradeSubscription = async (
    targetLevel: SubscriptionLevel, 
    isDebug: boolean = false
  ): Promise<boolean> => {
    if (!token) {
      setError('No authentication token available');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await updateSubscriptionLevel(token, targetLevel, isDebug);
      
      if (response.new_level && response.access_token) {
        // 如果返回了新的token，更新认证状态
        // 这里需要更新AuthContext中的用户信息
        await refreshSubscriptionInfo();
        
        // 如果是调试模式且成功，可以考虑重新登录以获取新的token
        if (isDebug && user) {
          // 注意：这里可能需要根据实际的AuthContext实现来调整
          console.log('Debug mode: Subscription level updated to', targetLevel);
        }
        
        return true;
      } else if (response.payment_url) {
        // 如果返回了支付URL，应该跳转到支付页面
        console.log('Payment URL:', response.payment_url);
        // TODO: 处理支付流程
        return false;
      } else {
        setError(response.message || 'Subscription update failed');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upgrade subscription');
      console.error('Failed to upgrade subscription:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const checkPermission = (requiredLevel: SubscriptionLevel): boolean => {
    if (!subscriptionInfo) return false;
    return checkSubscriptionPermission(subscriptionInfo.current_level, requiredLevel);
  };

  const getPlans = () => {
    return getSubscriptionPlans();
  };

  // 当用户登录状态改变时，刷新订阅信息
  useEffect(() => {
    if (user && token) {
      refreshSubscriptionInfo();
    } else {
      setSubscriptionInfo(null);
      setError(null);
    }
  }, [user, token]);

  const value: SubscriptionContextType = {
    subscriptionInfo,
    isLoading,
    error,
    refreshSubscriptionInfo,
    upgradeSubscription,
    checkPermission,
    getPlans,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

// 便捷的权限检查Hook
export const useSubscriptionPermission = (requiredLevel: SubscriptionLevel) => {
  const { checkPermission } = useSubscription();
  return checkPermission(requiredLevel);
};

// 便捷的订阅等级Hook
export const useSubscriptionLevel = () => {
  const { subscriptionInfo } = useSubscription();
  return subscriptionInfo?.current_level || 'Free';
};
