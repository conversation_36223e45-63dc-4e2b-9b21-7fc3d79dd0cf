from django.db import models
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from events.models import EventTemplate # Import EventTemplate from the events app

class User(AbstractUser):
    """
    扩展的用户模型

    在Django默认用户模型基础上添加订阅功能：
    - 订阅等级管理
    - 基于订阅等级的功能权限控制
    """

    # 订阅等级常量定义
    SUBSCRIPTION_FREE = 'Free'  # 免费版：基础功能
    SUBSCRIPTION_PRO = 'Pro'    # Pro版：高级功能
    SUBSCRIPTION_MAX = 'Max'    # Max版：全功能

    SUBSCRIPTION_CHOICES = [
        (SUBSCRIPTION_FREE, 'Free'),
        (SUBSCRIPTION_PRO, 'Pro'),
        (SUBSCRIPTION_MAX, 'Max'),
    ]

    # 用户订阅等级字段
    subscription_level = models.CharField(
        max_length=10,
        choices=SUBSCRIPTION_CHOICES,
        default=SUBSCRIPTION_FREE,
        help_text='用户订阅等级，决定可用功能范围'
    )

    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text=('The groups this user belongs to. A user will get all permissions '
                   'granted to each of their groups.'),
        related_name="core_user_set",
        related_query_name="user",
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name="core_user_permissions_set",
        related_query_name="user",
    )

    def __str__(self):
        return f"{self.username} ({self.subscription_level})"

class Room(models.Model):
    STATUS_WAITING = 'WAITING'
    STATUS_IN_PROGRESS = 'IN_PROGRESS' # Renamed from IN_GAME for clarity
    STATUS_FINISHED = 'FINISHED'
    STATUS_CHOICES = [
        (STATUS_WAITING, 'Waiting for players'),
        (STATUS_IN_PROGRESS, 'In Progress'),
        (STATUS_FINISHED, 'Finished'),
    ]

    room_code = models.CharField(max_length=10, unique=True, blank=True)
    host = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='hosted_rooms')
    participants = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='joined_rooms', blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_WAITING)
    created_at = models.DateTimeField(auto_now_add=True)

    # --- NEW: Link Room to an EventTemplate ---
    # This is the core of the refactor. Every room is now based on a template.
    event_template = models.ForeignKey(EventTemplate, on_delete=models.SET_NULL, null=True, blank=True)

    # --- NEW: Track the current step of the event flow ---
    current_step_order = models.PositiveIntegerField(default=0)

    # --- NEW: Subscription-based limitations ---
    max_participants = models.PositiveIntegerField(default=10, help_text="Maximum number of participants allowed")
    duration_hours = models.PositiveIntegerField(default=2, help_text="Room duration in hours")

    def set_limits_by_subscription(self, user):
        """
        根据用户订阅等级设置房间限制

        参数：
        - user: 用户对象，包含订阅等级信息

        限制规则：
        - Free: 10人，2小时
        - Pro: 500人，24小时
        - Max: 2000人，72小时
        """
        if user.subscription_level == user.SUBSCRIPTION_FREE:
            self.max_participants = 10
            self.duration_hours = 2
        elif user.subscription_level == user.SUBSCRIPTION_PRO:
            self.max_participants = 500
            self.duration_hours = 24
        elif user.subscription_level == user.SUBSCRIPTION_MAX:
            self.max_participants = 2000  # 实际上相当于无限制
            self.duration_hours = 72

    def __str__(self):
        template_name = self.event_template.name if self.event_template else "No Template"
        return f"Room {self.room_code} ({template_name})"

# Note: GameSession might become obsolete or be refactored later,
# as the game logic will be driven by EventSteps. We'll keep it for now.
class GameSession(models.Model):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='game_sessions')
    is_active = models.BooleanField(default=True)
