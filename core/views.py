# core/views.py

import uuid
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated # 导入权限类

from .models import User, Room # 导入Room模型
from .serializers import UserSerializer, RoomSerializer, CustomTokenObtainPairSerializer # 导入RoomSerializer
from events.models import EventTemplate
from rest_framework_simplejwt.views import TokenObtainPairView


# --- 添加以下代码 ---
class APIRootView(APIView):
    """
    API的根视图，提供一个欢迎信息和端点导航。
    """
    def get(self, request, *args, **kwargs):
        return Response({
            "message": "Welcome to the Tuanzi API!",
            "endpoints": {
                "GET /api/health-check/": "Check API status.",
                "POST /api/register/": "Create a new user.",
                "POST /api/token/": "Obtain an authentication token.",
                "POST /api/rooms/create/": "Create a new room (authentication required)."
            }
        })


# ... RegisterView 和 HealthCheckView 保持不变 ...
class HealthCheckView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"status": "ok"}, status=status.HTTP_200_OK)

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = []


class JoinRoomView(APIView):
    """
    允许登录用户通过房间号加入一个已存在的房间。
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        # 从请求体中获取房间号
        room_code = request.data.get('room_code')
        if not room_code:
            return Response(
                {"error": "Room code is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 查找对应的房间
            room = Room.objects.get(room_code__iexact=room_code) # iexact 忽略大小写
        except Room.DoesNotExist:
            return Response(
                {"error": "Room not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        user = request.user

        # 检查用户是否已经是参与者
        if room.participants.filter(id=user.id).exists():
            # 如果已经是，直接返回房间信息即可，不算作错误
            serializer = RoomSerializer(room)
            return Response(serializer.data, status=status.HTTP_200_OK)

        # 检查房间人数是否已满 (基于房间的max_participants限制)
        if room.participants.count() >= room.max_participants:
             return Response(
                {"error": f"Room is full. Maximum {room.max_participants} participants allowed."},
                status=status.HTTP_403_FORBIDDEN
            )

        # 将用户添加到参与者列表
        room.participants.add(user)

        serializer = RoomSerializer(room)
        return Response(serializer.data, status=status.HTTP_200_OK)
    

class RoomCreateView(APIView):
    """
    Creates a new room based on a selected EventTemplate.
    Enforces subscription-based limitations.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        template_id = request.data.get('template_id')
        if not template_id:
            return Response({"error": "Template ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            template = EventTemplate.objects.get(id=template_id)
        except EventTemplate.DoesNotExist:
            return Response({"error": "Template not found."}, status=status.HTTP_404_NOT_FOUND)

        host = request.user

        # --- SUBSCRIPTION-BASED LIMITATIONS ---
        # Check if template contains premium-only steps for free users
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            from events.models import EventStep
            premium_steps = template.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
            if premium_steps.exists():
                premium_step_names = [step.get_step_type_display() for step in premium_steps]
                return Response({
                    "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                    "premium_steps": premium_step_names,
                    "upgrade_required": True
                }, status=status.HTTP_403_FORBIDDEN)

        # --- FIX: Ensure the generated room_code is unique ---
        while True:
            # Generate a new random 6-character code
            room_code = uuid.uuid4().hex.upper()[:6]
            # Check if a room with this code already exists
            if not Room.objects.filter(room_code=room_code).exists():
                # If it doesn't exist, we found a unique code and can exit the loop
                break

        # Create the room with the guaranteed unique code
        room = Room.objects.create(
            host=host,
            event_template=template,
            room_code=room_code
        )

        # Set room limits based on user's subscription level
        room.set_limits_by_subscription(host)
        room.save()

        room.participants.add(host)

        serializer = RoomSerializer(room)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer