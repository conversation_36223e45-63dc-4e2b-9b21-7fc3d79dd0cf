"""
Django REST Framework 序列化器
处理API数据的序列化和反序列化
"""

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import User, Room

class UserSerializer(serializers.ModelSerializer):
    """
    用户序列化器
    用于用户注册和用户信息的序列化
    """
    class Meta:
        model = User
        fields = ('id', 'username', 'password')
        extra_kwargs = {'password': {'write_only': True}}  # 密码只写不读

    def create(self, validated_data):
        """
        创建新用户
        使用Django的create_user方法确保密码正确加密
        """
        user = User.objects.create_user(
            username=validated_data['username'],
            password=validated_data['password']
        )
        return user


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    自定义JWT Token序列化器
    在标准JWT Token中添加用户名和订阅等级信息
    """
    @classmethod
    def get_token(cls, user):
        """
        生成包含自定义声明的JWT Token

        添加的自定义字段：
        - username: 用户名
        - subscription_level: 订阅等级
        """
        token = super().get_token(user)
        # 添加自定义声明到JWT Token
        token['username'] = user.username
        token['subscription_level'] = user.subscription_level
        return token

class RoomSerializer(serializers.ModelSerializer):
    """
    房间序列化器
    用于房间信息的序列化，包括房间状态、参与者等信息
    """
    host = serializers.StringRelatedField()  # 房主信息（只读）
    participants = serializers.StringRelatedField(many=True, read_only=True)  # 参与者列表（只读）

    class Meta:
        model = Room
        # 包含房间的所有关键信息
        fields = ['id', 'room_code', 'host', 'participants', 'created_at', 'status']
        # 这些字段由系统自动管理，不允许客户端修改
        read_only_fields = ['room_code', 'host', 'participants']
