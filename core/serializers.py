# core/serializers.py

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import User, Room

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'username', 'password')
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        user = User.objects.create_user(
            username=validated_data['username'],
            password=validated_data['password']
        )
        return user


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        # Add custom claims
        token['username'] = user.username
        token['subscription_level'] = user.subscription_level
        return token

class RoomSerializer(serializers.ModelSerializer):
    host = serializers.StringRelatedField()
    participants = serializers.StringRelatedField(many=True, read_only=True)

    class Meta:
        model = Room
        # --- ADD 'status' TO THE FIELDS LIST ---
        fields = ['id', 'room_code', 'host', 'participants', 'created_at', 'status']
        read_only_fields = ['room_code', 'host', 'participants']
