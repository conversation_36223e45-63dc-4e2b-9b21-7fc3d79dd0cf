import json
import logging
import async<PERSON>

from typing import Optional
from channels.generic.websocket import Async<PERSON>ebsocketConsumer
from channels.db import database_sync_to_async

from .event_handlers import BaseEventHandler, EventHandlerFactory
from .models import Room
from events.models import EventStep
# CHANGE: Import helper functions from the new utils.py file
from .utils import get_room_with_template, advance_to_next_step, save_room

logger = logging.getLogger(__name__)

# The database helper functions that were here have been moved to utils.py

class RoomConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timer_task = None  # Store the timer task for cancellation
        self.current_handler: Optional[BaseEventHandler] = None  # 当前环节处理器

    async def connect(self):
        if not self.scope['user'] or not self.scope['user'].is_authenticated:
            logger.warning(f"Unauthenticated connection attempt")
            await self.close()
            return

        self.room_code = self.scope['url_route']['kwargs']['room_code']
        self.room_group_name = f'room_{self.room_code}'

        try:
            await self.channel_layer.group_add(self.room_group_name, self.channel_name)
            await self.accept()
            logger.info(f"User {self.scope['user'].username} connected to room {self.room_code}")
        except Exception as e:
            logger.error(f"Error connecting user to room {self.room_code}: {e}")
            await self.close()

    async def disconnect(self, close_code):
        _ = close_code  # 忽略未使用的参数
        # Cancel any running timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        # Cleanup current handler
        if self.current_handler:
            try:
                await self.current_handler.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up handler: {e}")

        if hasattr(self, 'room_group_name'):
            try:
                await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
                logger.info(f"User {self.scope['user'].username} disconnected from room {self.room_code}")
            except Exception as e:
                logger.error(f"Error disconnecting user from room {self.room_code}: {e}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            action = data.get('action')
            payload = data.get('payload', {})
            user = self.scope['user']

            logger.debug(f"Received action '{action}' from user {user.username} in room {self.room_code}")

            if action == 'next_step':
                await self.handle_next_step()
            elif action == 'send_message':
                await self.handle_chat_message(payload)
            elif action == 'restart_game':
                await self.handle_restart_game(payload)
            else:
                # 尝试让当前处理器处理自定义动作
                if self.current_handler:
                    handled = await self.current_handler.handle_custom_action(action, user, payload)
                    if handled:
                        return

                logger.warning(f"Unknown action '{action}' from user {user.username}")
                await self.send_error(f"Unknown action: {action}")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received from user {self.scope['user'].username}: {e}")
            await self.send_error("Invalid message format")
        except Exception as e:
            logger.error(f"Error processing message from user {self.scope['user'].username}: {e}")
            await self.send_error("Internal server error")
            
    async def handle_next_step(self):
        try:
            user = self.scope['user']
            logger.info(f"User {user.username} requesting next step in room {self.room_code}")

            room = await get_room_with_template(self.room_code)

            if not room:
                logger.error(f"Room {self.room_code} not found")
                await self.send_error('房间不存在。')
                return

            # Get host information safely
            room_host = await database_sync_to_async(lambda: room.host)()
            logger.info(f"Room found: {room.room_code}, host: {room_host}, current_step_order: {room.current_step_order}")

            if room_host != user:
                logger.warning(f"User {user.username} tried to start next step but is not host of room {self.room_code}")
                await self.send_error('只有房主才能开始下一环节。')
                return

            logger.info(f"User {user.username} is confirmed as host, advancing to next step")
            next_step = await advance_to_next_step(room)

            if not next_step:
                logger.info(f"All steps completed for room {self.room_code}")
                await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_event_over'})
                return

            logger.info(f"Starting step {next_step.order} ({next_step.step_type}) in room {self.room_code}")

            # 清理之前的处理器
            if self.current_handler:
                await self.current_handler.cleanup()

            # 创建新的环节处理器
            self.current_handler = EventHandlerFactory.create_handler(
                next_step.step_type, self.room_code, self
            )

            if not self.current_handler:
                logger.error(f"No handler found for step type {next_step.step_type} in room {self.room_code}")
                await self.send_error(f"不支持的环节类型: {next_step.step_type}")
                return

            # 启动环节
            game_data, error = await self.current_handler.start_step(room, next_step)
            if error:
                logger.error(f"Failed to start step {next_step.step_type} in room {self.room_code}: {error}")
                await self.send_error(error)
                return

            logger.info(f"Step {next_step.step_type} started successfully for room {self.room_code}")
            await self.channel_layer.group_send(self.room_group_name, {
                'type': 'broadcast_step_start',
                'payload': game_data
            })

            # 启动计时器
            await self.start_step_timer(next_step.duration)

        except Exception as e:
            logger.error(f"Error handling next_step in room {self.room_code}: {e}", exc_info=True)
            await self.send_error("处理下一环节时发生错误。")
            
    async def handle_chat_message(self, payload):
        try:
            message = payload.get('message', '').strip()
            user = self.scope['user']

            if not message:
                logger.warning(f"Empty message from user {user.username} in room {self.room_code}")
                return

            # 让当前处理器先处理消息
            if self.current_handler:
                handled = await self.current_handler.handle_message(user, payload)
                if handled:
                    return  # 消息已被处理器处理

            # 常规聊天消息处理
            await self.channel_layer.group_send(self.room_group_name, {
                'type': 'broadcast_chat_message',
                'payload': {'message': message, 'sender': user.username}
            })

        except Exception as e:
            logger.error(f"Error handling chat message from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("发送消息时发生错误。")
            
    async def handle_restart_game(self, payload):
        """Handle request to restart a game."""
        try:
            user = self.scope['user']
            logger.info(f"User {user.username} requesting restart in room {self.room_code}")

            room = await get_room_with_template(self.room_code)
            if not room:
                await self.send_error('房间不存在。')
                return

            # Check if user is host
            room_host = await database_sync_to_async(lambda: room.host)()
            if room_host != user:
                await self.send_error('只有房主才能重新开始游戏。')
                return

            # 让当前处理器处理重启
            if self.current_handler:
                game_data, error = await self.current_handler.handle_restart(user, payload)
                if error:
                    await self.send_error(error)
                    return

                if game_data:
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_step_start',
                        'payload': game_data
                    })

                    # 重新启动计时器
                    current_step = await database_sync_to_async(
                        lambda: room.event_template.steps.filter(order=room.current_step_order).first()
                    )()
                    if current_step:
                        await self.start_step_timer(current_step.duration)
            else:
                await self.send_error("当前没有活动的环节可以重启。")

        except Exception as e:
            logger.error(f"Error handling restart game from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("重新开始游戏时发生错误。")

    async def start_step_timer(self, duration_seconds):
        """Start a timer for the current step."""
        # Cancel any existing timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        # Start new timer
        self.timer_task = asyncio.create_task(self._step_timer(duration_seconds))

    async def _step_timer(self, duration_seconds):
        """Timer coroutine that handles step timeout."""
        try:
            await asyncio.sleep(duration_seconds)
            # Time's up - end the current step
            await self.handle_step_timeout()
        except asyncio.CancelledError:
            logger.info(f"Timer cancelled for room {self.room_code}")
            raise
        except Exception as e:
            logger.error(f"Error in step timer for room {self.room_code}: {e}")

    async def handle_step_timeout(self):
        """Handle when a step times out."""
        try:
            logger.info(f"Step timeout for room {self.room_code}")

            # 让当前处理器处理超时
            if self.current_handler:
                await self.current_handler.handle_timeout()
            else:
                # 如果没有处理器，执行默认的超时处理
                room = await get_room_with_template(self.room_code)
                if room:
                    room.status = Room.STATUS_WAITING
                    await save_room(room)
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_step_timeout',
                        'payload': {'room_status': room.status}
                    })

        except Exception as e:
            logger.error(f"Error handling step timeout for room {self.room_code}: {e}")

    async def send_error(self, message):
        """Sends an error message back to the originating client."""
        await self.send(text_data=json.dumps({'type': 'error', 'payload': {'message': message}}))

    # --- BROADCAST HANDLERS ---
    async def broadcast_step_start(self, event):
        payload = event['payload'].copy()
        step_type = payload.get('step_info', {}).get('step_type')
        if step_type == EventStep.STEP_GAME_PICTIONARY:
            if self.scope['user'].username != payload['drawer']:
                word = payload['word']
                payload['word'] = " ".join(["_" for _ in word if _ != ' '])
        await self.send(text_data=json.dumps({'type': 'step_started', 'payload': payload}))

    async def broadcast_chat_message(self, event): await self.send(text_data=json.dumps({'type': 'chat_message', 'payload': event['payload']}))
    async def broadcast_drawing_data(self, event): await self.send(text_data=json.dumps({'type': 'drawing_data', 'payload': event['payload']}))
    async def broadcast_round_over(self, event): await self.send(text_data=json.dumps({'type': 'round_over', 'payload': event['payload']}))
    async def broadcast_step_timeout(self, event): await self.send(text_data=json.dumps({'type': 'step_timeout', 'payload': event['payload']}))
    async def broadcast_event_over(self, _event): await self.send(text_data=json.dumps({'type': 'event_finished', 'payload': {'message': '所有环节已结束！感谢您的参与。'}}))

